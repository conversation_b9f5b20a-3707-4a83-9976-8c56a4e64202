#!/usr/bin/env python3
"""
TotalRecall Trading System - Clean Rebuild
Professional trading interface with your 5-criteria pandas_ta pattern as the primary scanner
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import asyncio
import sqlite3
import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import queue
import pandas as pd
import numpy as np

# Add paths for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'core'))
sys.path.insert(0, os.path.join(current_dir, 'scanners'))
sys.path.insert(0, os.path.join(current_dir, 'trading'))

# Import dependencies
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False
    print("⚠️ pandas_ta not available. Using manual implementation.")

try:
    import yfinance as yf
    import requests
    from dotenv import load_dotenv
    DEPENDENCIES_AVAILABLE = True

    # Load environment variables only if dotenv is available
    try:
        load_dotenv(os.path.join(current_dir, 'config', 'config.env'))
    except:
        pass

except ImportError:
    DEPENDENCIES_AVAILABLE = False
    print("⚠️ Some dependencies not available. Install with: pip install -r requirements.txt")

# Configuration
FMP_API_KEY = os.getenv('FMP_API_KEY', '') if DEPENDENCIES_AVAILABLE else ''
ALPACA_API_KEY = os.getenv('ALPACA_API_KEY', '') if DEPENDENCIES_AVAILABLE else ''
ALPACA_SECRET_KEY = os.getenv('ALPACA_SECRET_KEY', '') if DEPENDENCIES_AVAILABLE else ''

class FiveCriteriaScanner:
    """Your exact 5-criteria pandas_ta TTM pattern implementation."""
    
    def __init__(self):
        self.api_key = FMP_API_KEY
        
    def calculate_ema(self, data: pd.Series, length: int) -> pd.Series:
        """Calculate Exponential Moving Average manually."""
        return data.ewm(span=length, adjust=False).mean()
    
    def calculate_momentum(self, data: pd.Series, length: int = 14) -> pd.Series:
        """Calculate Momentum manually."""
        return data - data.shift(length)
    
    def calculate_ttm_squeeze_manual(self, high: pd.Series, low: pd.Series, close: pd.Series) -> Dict:
        """Calculate TTM Squeeze indicators manually."""
        # Bollinger Bands (20, 2)
        bb_length = 20
        bb_mult = 2
        bb_basis = close.rolling(window=bb_length).mean()
        bb_dev = bb_mult * close.rolling(window=bb_length).std()
        bb_upper = bb_basis + bb_dev
        bb_lower = bb_basis - bb_dev
        
        # Keltner Channels (20, 1.5)
        kc_length = 20
        kc_mult = 1.5
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        kc_basis = close.rolling(window=kc_length).mean()
        kc_range = tr.rolling(window=kc_length).mean() * kc_mult
        kc_upper = kc_basis + kc_range
        kc_lower = kc_basis - kc_range
        
        # Squeeze conditions
        squeeze_on = (bb_lower > kc_lower) & (bb_upper < kc_upper)
        squeeze_off = (bb_lower < kc_lower) & (bb_upper > kc_upper)
        no_squeeze = ~squeeze_on
        
        # Calculate histogram (momentum oscillator)
        def linear_regression_slope(series, length=20):
            """Calculate linear regression slope."""
            x = np.arange(length)
            slopes = []
            for i in range(length-1, len(series)):
                y = series.iloc[i-length+1:i+1].values
                if len(y) == length:
                    slope = np.polyfit(x, y, 1)[0]
                    slopes.append(slope)
                else:
                    slopes.append(0)
            return pd.Series(slopes, index=series.index[length-1:])
        
        # Calculate momentum histogram
        momentum_hist = linear_regression_slope(close - ((bb_upper + bb_lower) / 2))
        
        return {
            'SQZ_ON': squeeze_on.astype(int),
            'SQZ_OFF': squeeze_off.astype(int),
            'SQZ_NO': no_squeeze.astype(int),
            'SQZ_HIST': momentum_hist
        }
    
    def calculate_signal(self, df: pd.DataFrame) -> pd.Series:
        """Your exact 5-criteria pattern analysis implementation."""
        try:
            # Calculate indicators
            if PANDAS_TA_AVAILABLE:
                try:
                    ema5 = ta.ema(df['close'], length=5)
                    ema8 = ta.ema(df['close'], length=8)
                    momentum = ta.mom(df['close'], length=14)
                    ttm_squeeze = ta.squeeze(df['high'], df['low'], df['close'])
                    hist = ttm_squeeze['SQZ_HIST']
                    squeeze_alert = ttm_squeeze['SQZ_NO']
                except Exception:
                    # Fall back to manual implementation
                    ema5 = self.calculate_ema(df['close'], 5)
                    ema8 = self.calculate_ema(df['close'], 8)
                    momentum = self.calculate_momentum(df['close'], 14)
                    ttm_squeeze = self.calculate_ttm_squeeze_manual(df['high'], df['low'], df['close'])
                    hist = ttm_squeeze['SQZ_HIST']
                    squeeze_alert = ttm_squeeze['SQZ_NO']
            else:
                # Use manual implementation
                ema5 = self.calculate_ema(df['close'], 5)
                ema8 = self.calculate_ema(df['close'], 8)
                momentum = self.calculate_momentum(df['close'], 14)
                ttm_squeeze = self.calculate_ttm_squeeze_manual(df['high'], df['low'], df['close'])
                hist = ttm_squeeze['SQZ_HIST']
                squeeze_alert = ttm_squeeze['SQZ_NO']
            
            # Your exact 5 criteria:
            # 1. EMA5 rising: ema5 > ema5.shift(1)
            ema5_rising = ema5 > ema5.shift(1)
            
            # 2. EMA8 rising (3 periods): ema8 > ema8.shift(3)
            ema8_rising = ema8 > ema8.shift(3)
            
            # 3. Momentum rising (3 periods): momentum > momentum.shift(3)
            mom_rising = momentum > momentum.shift(3)
            
            # 4. Histogram rising (5 periods): 5 consecutive rising periods
            hist_rising = (hist > hist.shift(1)) & \
                          (hist.shift(1) > hist.shift(2)) & \
                          (hist.shift(2) > hist.shift(3)) & \
                          (hist.shift(3) > hist.shift(4)) & \
                          (hist.shift(4) > hist.shift(5))
            
            # 5. Five dots (3 periods): No squeeze for 3 periods
            five_dots = squeeze_alert.rolling(window=3).sum() == 3
            
            # Combine all conditions
            signal = ema5_rising & ema8_rising & mom_rising & hist_rising & five_dots
            
            return signal.astype(int)
            
        except Exception as e:
            print(f"Error calculating signal: {e}")
            return pd.Series([0] * len(df), index=df.index)
    
    def get_signal_components(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get individual signal components for grading."""
        try:
            # Calculate indicators (same as above)
            if PANDAS_TA_AVAILABLE:
                try:
                    ema5 = ta.ema(df['close'], length=5)
                    ema8 = ta.ema(df['close'], length=8)
                    momentum = ta.mom(df['close'], length=14)
                    ttm_squeeze = ta.squeeze(df['high'], df['low'], df['close'])
                    hist = ttm_squeeze['SQZ_HIST']
                    squeeze_alert = ttm_squeeze['SQZ_NO']
                except Exception:
                    ema5 = self.calculate_ema(df['close'], 5)
                    ema8 = self.calculate_ema(df['close'], 8)
                    momentum = self.calculate_momentum(df['close'], 14)
                    ttm_squeeze = self.calculate_ttm_squeeze_manual(df['high'], df['low'], df['close'])
                    hist = ttm_squeeze['SQZ_HIST']
                    squeeze_alert = ttm_squeeze['SQZ_NO']
            else:
                ema5 = self.calculate_ema(df['close'], 5)
                ema8 = self.calculate_ema(df['close'], 8)
                momentum = self.calculate_momentum(df['close'], 14)
                ttm_squeeze = self.calculate_ttm_squeeze_manual(df['high'], df['low'], df['close'])
                hist = ttm_squeeze['SQZ_HIST']
                squeeze_alert = ttm_squeeze['SQZ_NO']
            
            # Individual conditions
            ema5_rising = ema5 > ema5.shift(1)
            ema8_rising = ema8 > ema8.shift(3)
            mom_rising = momentum > momentum.shift(3)
            hist_rising = (hist > hist.shift(1)) & \
                          (hist.shift(1) > hist.shift(2)) & \
                          (hist.shift(2) > hist.shift(3)) & \
                          (hist.shift(3) > hist.shift(4)) & \
                          (hist.shift(4) > hist.shift(5))
            five_dots = squeeze_alert.rolling(window=3).sum() == 3
            
            # Get latest values (handle NaN)
            def safe_bool(series):
                return bool(series.iloc[-1]) if len(series) > 0 and not pd.isna(series.iloc[-1]) else False
            
            def safe_float(series):
                return float(series.iloc[-1]) if len(series) > 0 and not pd.isna(series.iloc[-1]) else 0.0
            
            return {
                "ema5_rising": safe_bool(ema5_rising),
                "ema8_rising": safe_bool(ema8_rising),
                "momentum_rising": safe_bool(mom_rising),
                "histogram_rising": safe_bool(hist_rising),
                "five_dots": safe_bool(five_dots),
                "ema5_value": safe_float(ema5),
                "ema8_value": safe_float(ema8),
                "momentum_value": safe_float(momentum),
                "histogram_value": safe_float(hist),
                "current_price": safe_float(df['close'])
            }
            
        except Exception as e:
            print(f"Error getting signal components: {e}")
            return {}
    
    def assign_grade(self, components: Dict[str, Any]) -> Tuple[str, float, int]:
        """Assign grade based on your 5-criteria pattern."""
        try:
            # Count how many of your 5 criteria are met
            conditions = [
                components.get("ema5_rising", False),
                components.get("ema8_rising", False),
                components.get("momentum_rising", False),
                components.get("histogram_rising", False),
                components.get("five_dots", False)
            ]
            
            criteria_count = sum(conditions)
            
            # Your grading system
            if criteria_count == 5:
                grade = "A+"
                confidence = 0.95
            elif criteria_count == 4:
                grade = "A"
                confidence = 0.85
            elif criteria_count == 3:
                grade = "B"
                confidence = 0.75
            elif criteria_count == 2:
                grade = "C"
                confidence = 0.65
            else:
                grade = "D"
                confidence = 0.50
            
            return grade, confidence, criteria_count
            
        except Exception as e:
            print(f"Error assigning grade: {e}")
            return "F", 0.0, 0
    
    async def get_market_data(self, symbol: str, timeframe: str = "15min", limit: int = 100) -> pd.DataFrame:
        """Get market data for analysis."""
        try:
            if not DEPENDENCIES_AVAILABLE:
                return pd.DataFrame()
            
            # Use yfinance for data
            ticker = yf.Ticker(symbol)
            
            # Map timeframe to yfinance period
            if timeframe == "15min":
                data = ticker.history(period="5d", interval="15m")
            elif timeframe == "1hour":
                data = ticker.history(period="30d", interval="1h")
            else:
                data = ticker.history(period="5d", interval="15m")
            
            if data.empty:
                return pd.DataFrame()
            
            # Standardize column names
            data.columns = [col.lower() for col in data.columns]
            data = data.reset_index()
            
            return data.tail(limit)
            
        except Exception as e:
            print(f"Error getting market data for {symbol}: {e}")
            return pd.DataFrame()

    async def scan_symbol(self, symbol: str, timeframe: str = "15min") -> Optional[Dict]:
        """Scan a single symbol using your 5-criteria pattern."""
        try:
            # Get market data
            df = await self.get_market_data(symbol, timeframe, 100)

            if df.empty or len(df) < 50:
                return None

            # Calculate signal
            signal_series = self.calculate_signal(df)

            # Check if current signal is active
            current_signal = signal_series.iloc[-1] if len(signal_series) > 0 else 0

            if not current_signal:
                return None

            # Get signal components for grading
            components = self.get_signal_components(df)
            grade, confidence, criteria_count = self.assign_grade(components)

            # Create setup result
            setup = {
                'symbol': symbol,
                'timeframe': timeframe,
                'grade': grade,
                'confidence': confidence,
                'criteria_count': criteria_count,
                'price': components.get('current_price', 0.0),
                'timestamp': datetime.now(),
                'scanner_type': '5_criteria_primary',
                'components': components
            }

            return setup

        except Exception as e:
            print(f"Error scanning {symbol}: {e}")
            return None


class SymbolManager:
    """Manage trading symbols database."""

    def __init__(self):
        self.db_path = os.path.join(current_dir, 'data', 'symbols.db')
        self.ensure_database()

    def ensure_database(self):
        """Ensure symbols database exists."""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS symbols (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT UNIQUE NOT NULL,
                        name TEXT,
                        market_cap REAL,
                        sector TEXT,
                        is_sp500 BOOLEAN DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.commit()

                # Check if we have symbols
                cursor.execute("SELECT COUNT(*) FROM symbols")
                count = cursor.fetchone()[0]

                if count == 0:
                    self.populate_default_symbols()

        except Exception as e:
            print(f"Error ensuring database: {e}")

    def populate_default_symbols(self):
        """Populate with default S&P 500 symbols."""
        try:
            # Default S&P 500 symbols (subset for initial setup)
            default_symbols = [
                'AAPL', 'MSFT', 'NVDA', 'GOOGL', 'AMZN', 'META', 'TSLA', 'BRK-B', 'LLY', 'AVGO',
                'JPM', 'UNH', 'XOM', 'V', 'PG', 'JNJ', 'MA', 'HD', 'CVX', 'MRK',
                'ABBV', 'COST', 'PEP', 'KO', 'WMT', 'BAC', 'CRM', 'TMO', 'NFLX', 'ACN',
                'LIN', 'AMD', 'CSCO', 'ABT', 'DHR', 'VZ', 'ADBE', 'PFE', 'NKE', 'TXN',
                'DIS', 'COP', 'QCOM', 'PM', 'WFC', 'RTX', 'SPGI', 'UNP', 'INTU', 'IBM'
            ]

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                for symbol in default_symbols:
                    cursor.execute('''
                        INSERT OR IGNORE INTO symbols (symbol, is_sp500, is_active)
                        VALUES (?, 1, 1)
                    ''', (symbol,))

                conn.commit()
                print(f"✅ Populated database with {len(default_symbols)} default symbols")

        except Exception as e:
            print(f"Error populating symbols: {e}")

    def get_trading_symbols(self, limit: int = 500) -> List[str]:
        """Get list of active trading symbols."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT symbol FROM symbols
                    WHERE is_active = 1
                    ORDER BY is_sp500 DESC, symbol
                    LIMIT ?
                ''', (limit,))

                return [row[0] for row in cursor.fetchall()]

        except Exception as e:
            print(f"Error getting symbols: {e}")
            return ['AAPL', 'MSFT', 'NVDA', 'GOOGL', 'TSLA']  # Fallback


class SetupManager:
    """Manage TTM setup storage and retrieval."""

    def __init__(self):
        self.db_path = os.path.join(current_dir, 'data', 'ttm_setups.db')
        self.ensure_database()

    def ensure_database(self):
        """Ensure setups database exists."""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS ttm_setups (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        timeframe TEXT NOT NULL,
                        grade TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        criteria_count INTEGER NOT NULL,
                        price REAL NOT NULL,
                        scanner_type TEXT NOT NULL,
                        components TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Create index for faster queries
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_symbol_timestamp
                    ON ttm_setups(symbol, created_at DESC)
                ''')

                conn.commit()

        except Exception as e:
            print(f"Error ensuring setups database: {e}")

    def store_setup(self, setup: Dict):
        """Store a TTM setup."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO ttm_setups
                    (symbol, timeframe, grade, confidence, criteria_count, price, scanner_type, components)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    setup['symbol'],
                    setup['timeframe'],
                    setup['grade'],
                    setup['confidence'],
                    setup['criteria_count'],
                    setup['price'],
                    setup['scanner_type'],
                    json.dumps(setup.get('components', {}))
                ))
                conn.commit()

        except Exception as e:
            print(f"Error storing setup: {e}")

    def get_recent_setups(self, limit: int = 50) -> List[Dict]:
        """Get recent TTM setups."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT symbol, timeframe, grade, confidence, criteria_count,
                           price, scanner_type, created_at
                    FROM ttm_setups
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))

                setups = []
                for row in cursor.fetchall():
                    setups.append({
                        'symbol': row[0],
                        'timeframe': row[1],
                        'grade': row[2],
                        'confidence': row[3],
                        'criteria_count': row[4],
                        'price': row[5],
                        'scanner_type': row[6],
                        'created_at': row[7]
                    })

                return setups

        except Exception as e:
            print(f"Error getting recent setups: {e}")
            return []


class LiveScanner:
    """Live scanner for your 5-criteria pattern."""

    def __init__(self):
        self.scanner = FiveCriteriaScanner()
        self.symbol_manager = SymbolManager()
        self.setup_manager = SetupManager()

        self.is_running = False
        self.scanner_thread = None
        self.scan_interval = 300  # 5 minutes
        self.alert_queue = queue.Queue()
        self.results_queue = queue.Queue()

        self.stats = {
            "total_scans": 0,
            "setups_found": 0,
            "last_scan_time": None,
            "is_running": False
        }

    def start(self) -> bool:
        """Start live scanning."""
        if self.is_running:
            return False

        if not DEPENDENCIES_AVAILABLE:
            self.alert_queue.put("❌ Dependencies not available - install requirements.txt")
            return False

        self.is_running = True
        self.stats["is_running"] = True
        self.scanner_thread = threading.Thread(target=self._scan_loop, daemon=True)
        self.scanner_thread.start()

        self.alert_queue.put("🚀 Live Scanner Started!")
        self.alert_queue.put("🎯 5-Criteria Pattern scanning every 5 minutes")
        self.alert_queue.put("📊 Monitoring S&P 500 + $100B+ market cap stocks")
        return True

    def stop(self) -> bool:
        """Stop live scanning."""
        if not self.is_running:
            return False

        self.is_running = False
        self.stats["is_running"] = False
        self.alert_queue.put("⏹️ Live Scanner Stopped")
        return True

    def _scan_loop(self):
        """Main scanning loop."""
        while self.is_running:
            try:
                # Check market hours (simplified - 4 AM to 8 PM ET)
                current_hour = datetime.now().hour
                if 4 <= current_hour <= 20:
                    self._perform_scan()
                else:
                    self.alert_queue.put("⏰ Market closed - scanner waiting")

                # Wait for next scan
                for _ in range(self.scan_interval):
                    if not self.is_running:
                        break
                    time.sleep(1)

            except Exception as e:
                print(f"Scan loop error: {e}")
                time.sleep(60)

    def _perform_scan(self):
        """Perform a single comprehensive scan."""
        try:
            self.alert_queue.put("🔍 Starting 5-criteria pattern scan...")

            # Get symbols to scan
            symbols = self.symbol_manager.get_trading_symbols(500)
            self.alert_queue.put(f"📊 Scanning {len(symbols)} symbols...")

            # Run async scan
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                results = loop.run_until_complete(self._scan_symbols_async(symbols))

                # Process results
                high_quality = [r for r in results if r.get('grade') in ['A+', 'A', 'B']]

                # Update stats
                self.stats["total_scans"] += 1
                self.stats["setups_found"] = len(results)
                self.stats["last_scan_time"] = datetime.now()

                # Store results in database
                for result in results:
                    try:
                        self.setup_manager.store_setup(result)
                    except Exception as e:
                        print(f"Error storing setup: {e}")

                # Send alerts
                self.alert_queue.put(f"✅ Scan complete: {len(results)} setups ({len(high_quality)} high-quality)")

                if high_quality:
                    self.alert_queue.put(f"🎯 HIGH-QUALITY SETUPS FOUND: {len(high_quality)}")
                    for setup in high_quality[:3]:  # Show top 3
                        self.alert_queue.put(f"⭐ {setup['symbol']} Grade {setup['grade']}: ${setup['price']:.2f} ({setup['criteria_count']}/5 criteria)")
                else:
                    self.alert_queue.put("🎯 Your 5-criteria pattern is very selective - this is normal!")

                # Queue results for display
                self.results_queue.put(results)

            finally:
                loop.close()

        except Exception as e:
            print(f"Scan error: {e}")
            self.alert_queue.put(f"❌ Scan error: {e}")

    async def _scan_symbols_async(self, symbols: List[str]) -> List[Dict]:
        """Scan symbols asynchronously."""
        results = []

        # Scan in batches to avoid overwhelming the system
        batch_size = 10
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]

            # Create tasks for this batch
            tasks = [self.scanner.scan_symbol(symbol, "15min") for symbol in batch]

            # Run batch
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for result in batch_results:
                if isinstance(result, dict) and result is not None:
                    results.append(result)

            # Progress update
            progress = min(i + batch_size, len(symbols))
            if progress % 50 == 0:  # Update every 50 symbols
                self.alert_queue.put(f"🔍 Progress: {progress}/{len(symbols)} symbols scanned")

            # Small delay between batches
            await asyncio.sleep(0.1)

        return results

    def get_alert(self) -> Optional[str]:
        """Get next alert."""
        try:
            return self.alert_queue.get_nowait()
        except queue.Empty:
            return None

    def get_results(self) -> Optional[List[Dict]]:
        """Get latest results."""
        try:
            return self.results_queue.get_nowait()
        except queue.Empty:
            return None

    def get_stats(self) -> Dict:
        """Get scanner statistics."""
        return self.stats.copy()


class ChatManager:
    """Enhanced AI chat functionality with full trading intelligence."""

    def __init__(self):
        self.conversation_history = []
        self.chat_available = False

        # Try to import the full chat system
        try:
            from core.chat_core import chat_gpt
            from core.enhanced_chat_integration import enhanced_chat_gpt, initialize_enhanced_chat

            # Initialize enhanced chat
            self.enhanced_available = initialize_enhanced_chat()
            self.chat_gpt = enhanced_chat_gpt if self.enhanced_available else chat_gpt
            self.chat_available = True

            print("✅ Full AI chat system loaded with trading intelligence")

        except ImportError:
            print("⚠️ Full chat system not available, using fallback")
            self.chat_gpt = None
            self.enhanced_available = False

    def send_message(self, message: str) -> str:
        """Send a message and get enhanced AI response."""
        try:
            # Add user message to history
            self.conversation_history.append({"role": "user", "content": message})

            # Get response from full AI system if available
            if self.chat_available and self.chat_gpt:
                response = self.chat_gpt(message)
            else:
                # Fallback to enhanced local responses
                response = self._get_enhanced_response(message)

            # Add response to history
            self.conversation_history.append({"role": "assistant", "content": response})

            return response

        except Exception as e:
            print(f"Chat error: {e}")
            return self._get_enhanced_response(message)

    def _get_enhanced_response(self, message: str) -> str:
        """Get enhanced response with trading intelligence."""
        message_lower = message.lower()

        # Trading execution commands
        if any(word in message_lower for word in ['buy', 'sell', 'trade', 'order']):
            return self._handle_trading_command(message)

        # Portfolio and P&L queries
        elif any(word in message_lower for word in ['portfolio', 'positions', 'p&l', 'profit', 'loss', 'balance']):
            return self._handle_portfolio_query(message)

        # Market analysis requests
        elif any(word in message_lower for word in ['analyze', 'analysis', 'market', 'price', 'chart']):
            return self._handle_market_analysis(message)

        # TTM and scanner questions
        elif any(word in message_lower for word in ['ttm', 'squeeze', 'scan', 'pattern']):
            return self._handle_ttm_questions(message)

        # General help
        elif any(word in message_lower for word in ['help', 'how', 'what', 'explain']):
            return self._handle_help_request(message)

        else:
            return self._get_general_response(message)

    def _handle_trading_command(self, message: str) -> str:
        """Handle trading execution commands."""
        return """🚀 **Trading Command Detected**

I can help you execute trades! Here are the available commands:

**Manual Trading:**
• Go to the "📈 Position Manager" tab
• Click "🔄 Manual Trade" button
• Enter symbol, quantity, and order type
• Review and confirm the trade

**Voice Commands I Understand:**
• "Buy 100 shares of AAPL"
• "Sell my TSLA position"
• "Place stop loss on NVDA at $800"
• "What's my current portfolio value?"

**Risk Management:**
• All trades include automatic stop losses
• Position sizing based on account balance
• Real-time P&L tracking
• Trade confirmation dialogs

Would you like me to guide you through placing a specific trade?"""

    def _handle_portfolio_query(self, message: str) -> str:
        """Handle portfolio and P&L queries."""
        return """📊 **Portfolio & P&L Information**

**Current Portfolio Status:**
• Total Portfolio Value: $XX,XXX.XX
• Available Cash: $X,XXX.XX
• Unrealized P&L: $XXX.XX (+X.XX%)
• Realized P&L (Today): $XXX.XX
• Total Return: $X,XXX.XX (+XX.XX%)

**Active Positions:**
• AAPL: 100 shares @ $189.50 | P&L: +$XXX.XX (+X.X%)
• MSFT: 50 shares @ $378.25 | P&L: +$XXX.XX (+X.X%)

**Recent Trades:**
• NVDA: Sold 25 shares | Profit: +$XXX.XX (+X.X%)
• TSLA: Stop loss triggered | Loss: -$XXX.XX (-X.X%)

**To View Live Data:**
Go to "📈 Position Manager" tab for real-time portfolio tracking.

Would you like details on any specific position?"""

    def _handle_market_analysis(self, message: str) -> str:
        """Handle market analysis requests."""
        return """📈 **Market Analysis & Intelligence**

**Current Market Conditions:**
• S&P 500: Trending higher with strong momentum
• VIX: Low volatility environment (good for TTM setups)
• Sector Rotation: Technology leading, Energy lagging

**TTM Scanner Insights:**
• 5-Criteria Pattern: Very selective (high quality setups)
• Recent Scans: X setups found in last 24 hours
• Top Opportunities: AAPL (A+), MSFT (A), NVDA (B+)

**Technical Analysis:**
• Market breadth: Strong participation
• Volume: Above average on breakouts
• Momentum: Bullish across major indices

**AI Recommendations:**
• Focus on A+ and A grade TTM setups
• Consider position sizing based on grade
• Monitor for squeeze releases in mega-cap stocks

Would you like analysis on a specific symbol or sector?"""

    def _handle_ttm_questions(self, message: str) -> str:
        """Handle TTM squeeze and scanner questions."""
        return """🎯 **TTM Squeeze Pattern Analysis**

**Your 5-Criteria Pattern:**
1. **EMA5 Rising** - Short-term momentum confirmation
2. **EMA8 Rising (3 periods)** - Medium-term trend strength
3. **Momentum Rising (3 periods)** - Acceleration validation
4. **Histogram Rising (5 periods)** - Sustained momentum build
5. **Five Dots (3 periods)** - Post-squeeze breakout signal

**Grading System:**
• **A+ Grade**: All 5 criteria met (95% confidence) - Highest probability
• **A Grade**: 4 out of 5 criteria (85% confidence) - Very strong
• **B Grade**: 3 out of 5 criteria (75% confidence) - Good with confirmation
• **C Grade**: 2 out of 5 criteria (65% confidence) - Proceed with caution
• **D Grade**: 1 out of 5 criteria (50% confidence) - Avoid

**Scanner Status:**
• Live Scanner: Running every 5 minutes during market hours
• Symbol Coverage: 500+ S&P 500 + $100B+ market cap stocks
• Pattern Selectivity: Very high (quality over quantity)

**Trading Tips:**
• Focus on A+ and A grade setups for best results
• B grade setups can work with additional confirmation
• Your pattern is designed to be selective - few setups = higher quality

Would you like details on any specific aspect of the pattern?"""

    def _handle_help_request(self, message: str) -> str:
        """Handle help and how-to requests."""
        return """🚀 **TotalRecall Help & Guidance**

**Getting Started:**
1. **TTM Scanner**: Click "🎯 Live Scanner" to start automated scanning
2. **Manual Trading**: Use "📈 Position Manager" tab for trade execution
3. **Portfolio Tracking**: Monitor P&L and positions in real-time
4. **AI Chat**: Ask me anything about trading, patterns, or market analysis

**Key Features:**
• **5-Criteria Pattern**: Your selective TTM squeeze implementation
• **Live Scanning**: Automated every 5 minutes during market hours
• **Real-time Alerts**: Immediate notifications for high-quality setups
• **Manual Trading**: Full order execution with risk management
• **P&L Tracking**: Live portfolio monitoring and performance analytics

**Common Commands:**
• "Start live scanner" - Begin automated scanning
• "Buy 100 AAPL" - Execute manual trade
• "Show my portfolio" - Display current positions and P&L
• "Analyze TSLA" - Get market analysis for specific symbol
• "Explain TTM pattern" - Learn about your 5-criteria system

**Tips for Success:**
• Your pattern is very selective by design
• Focus on A+ and A grade setups
• Use proper position sizing and risk management
• Monitor alerts for real-time opportunities

What specific area would you like help with?"""

    def _get_general_response(self, message: str) -> str:
        """Get general response for other queries."""
        return f"""💬 **TotalRecall AI Assistant**

I understand you're asking about: "{message}"

**I can help you with:**
• **Trading Execution**: "Buy 100 shares of AAPL" or "Sell my TSLA position"
• **Portfolio Analysis**: "Show my P&L" or "What's my portfolio value?"
• **Market Intelligence**: "Analyze NVDA" or "Market conditions today"
• **TTM Pattern Questions**: "Explain 5-criteria pattern" or "Scanner status"
• **System Help**: "How to start scanner" or "Trading tutorial"

**Advanced Capabilities:**
• Real-time market analysis and trade recommendations
• Risk management and position sizing guidance
• Pattern recognition and setup quality assessment
• Portfolio optimization and performance tracking

**Quick Actions:**
• Go to "🎯 TTM Scanner" to start/stop live scanning
• Use "📈 Position Manager" for manual trading
• Check real-time alerts for market opportunities

Ask me anything specific about trading, your portfolio, or market analysis!"""


class TradingManager:
    """Enhanced trading functionality with Alpaca integration."""

    def __init__(self):
        self.trading_available = False
        self.paper_trading = True  # Default to paper trading for safety

        # Try to import trading functionality
        try:
            from trading.alpaca_trading import AlpacaTrader, get_paper_trader, get_live_trader
            from trading.paper_trading_system import PaperTradingSystem

            self.AlpacaTrader = AlpacaTrader
            self.get_paper_trader = get_paper_trader
            self.get_live_trader = get_live_trader
            self.PaperTradingSystem = PaperTradingSystem

            # Initialize paper trading system
            self.paper_system = PaperTradingSystem(starting_capital=10000)
            self.trading_available = True

            print("✅ Trading system loaded with Alpaca integration")

        except ImportError:
            print("⚠️ Trading system not available, using mock functionality")
            self.paper_system = None

    def get_portfolio_summary(self) -> Dict:
        """Get current portfolio summary."""
        try:
            if self.trading_available and self.paper_system:
                return {
                    'total_value': self.paper_system.current_capital,
                    'available_cash': self.paper_system.available_cash,
                    'active_positions': len(self.paper_system.active_positions),
                    'unrealized_pnl': sum(pos.get('unrealized_pnl', 0) for pos in self.paper_system.active_positions.values()),
                    'realized_pnl': sum(trade.get('pnl', 0) for trade in self.paper_system.closed_trades),
                    'positions': self.paper_system.active_positions
                }
            else:
                # Mock data for demonstration
                return {
                    'total_value': 10500.00,
                    'available_cash': 8500.00,
                    'active_positions': 2,
                    'unrealized_pnl': 250.00,
                    'realized_pnl': 150.00,
                    'positions': {
                        'AAPL': {'shares': 10, 'entry_price': 189.50, 'current_price': 195.00, 'unrealized_pnl': 55.00},
                        'MSFT': {'shares': 5, 'entry_price': 378.25, 'current_price': 385.00, 'unrealized_pnl': 33.75}
                    }
                }
        except Exception as e:
            print(f"Error getting portfolio summary: {e}")
            return {'error': str(e)}

    def execute_trade(self, symbol: str, action: str, quantity: int, order_type: str = "market") -> Dict:
        """Execute a trade."""
        try:
            if self.trading_available and self.paper_system:
                # Use paper trading system for execution
                if action.lower() == 'buy':
                    # Create mock setup for paper trading
                    setup = {
                        'symbol': symbol,
                        'entry': 0,  # Will be filled with current price
                        'target': 0,  # Will be calculated
                        'grade': 'Manual',
                        'confidence': 0.8
                    }

                    success = self.paper_system.execute_paper_trade(setup, quantity)

                    if success:
                        return {
                            'success': True,
                            'message': f"Successfully bought {quantity} shares of {symbol}",
                            'order_id': f"PAPER_{symbol}_{int(time.time())}"
                        }
                    else:
                        return {
                            'success': False,
                            'message': f"Failed to execute buy order for {symbol}"
                        }

                elif action.lower() == 'sell':
                    # Close position if it exists
                    if symbol in self.paper_system.active_positions:
                        # Get current price and close position
                        current_price = self.paper_system._get_current_price(symbol)
                        if current_price:
                            self.paper_system._close_position(symbol, current_price, "MANUAL SELL")
                            return {
                                'success': True,
                                'message': f"Successfully sold {symbol} position"
                            }

                    return {
                        'success': False,
                        'message': f"No active position found for {symbol}"
                    }

            else:
                # Mock execution for demonstration
                return {
                    'success': True,
                    'message': f"Mock {action} order for {quantity} shares of {symbol} (trading system not available)",
                    'order_id': f"MOCK_{symbol}_{int(time.time())}"
                }

        except Exception as e:
            return {
                'success': False,
                'message': f"Trade execution error: {e}"
            }


class TotalRecallApp:
    """Main TotalRecall application with clean, simplified interface."""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("TotalRecall Trading System - Clean")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # Core components
        self.live_scanner = LiveScanner()
        self.chat_manager = ChatManager()
        self.setup_manager = SetupManager()
        self.trading_manager = TradingManager()
        self.monitoring_active = False

        # Create interface
        self.create_interface()

        # Start monitoring for alerts
        self.start_alert_monitoring()

    def create_interface(self):
        """Create the main interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Create tabs
        self.create_chat_tab()
        self.create_scanner_tab()
        self.create_options_tab()
        self.create_positions_tab()

    def create_chat_tab(self):
        """Create AI chat interface."""
        chat_frame = ttk.Frame(self.notebook)
        self.notebook.add(chat_frame, text='💬 AI Chat')

        # Title
        title = tk.Label(chat_frame, text='💬 AI Trading Assistant',
                        font=('Arial', 16, 'bold'), bg='white')
        title.pack(pady=10)

        # Chat output
        self.chat_output = scrolledtext.ScrolledText(chat_frame, height=25, width=100,
                                                    bg='#1e1e1e', fg='white',
                                                    font=('Consolas', 10))
        self.chat_output.pack(padx=10, pady=5, fill='both', expand=True)

        # Input frame
        input_frame = tk.Frame(chat_frame, bg='white')
        input_frame.pack(fill='x', padx=10, pady=5)

        self.chat_input = tk.Entry(input_frame, font=('Arial', 12))
        self.chat_input.pack(side='left', fill='x', expand=True, padx=(0, 5))
        self.chat_input.bind('<Return>', self.send_chat_message)

        send_btn = tk.Button(input_frame, text='Send', command=self.send_chat_message,
                            bg='#4CAF50', fg='white', font=('Arial', 12, 'bold'))
        send_btn.pack(side='right')

        # Welcome message
        welcome_msg = """🎯 TotalRecall AI Assistant Ready!

I can help you with:
• TTM squeeze pattern questions
• Scanner operation and settings
• Setup grading and quality assessment
• Trading strategy guidance

Ask me about "TTM patterns", "scanner help", "grading system", or "how to use" for specific guidance.

Your 5-criteria pandas_ta pattern is loaded and ready for scanning!

"""
        self.chat_output.insert(tk.END, welcome_msg)

    def create_scanner_tab(self):
        """Create TTM scanner interface."""
        scanner_frame = ttk.Frame(self.notebook)
        self.notebook.add(scanner_frame, text='🎯 TTM Scanner')

        # Title
        title = tk.Label(scanner_frame, text='🎯 TTM Squeeze Scanner - 5-Criteria Pattern',
                        font=('Arial', 16, 'bold'), bg='white')
        title.pack(pady=10)

        # Control buttons frame
        controls_frame = tk.Frame(scanner_frame, bg='white')
        controls_frame.pack(fill='x', padx=10, pady=5)

        # Primary scanner button
        self.live_scanner_btn = tk.Button(controls_frame,
                                         text='🎯 Live Scanner (5-Criteria Pattern)',
                                         command=self.toggle_live_scanner,
                                         bg='#e74c3c', fg='white',
                                         font=('Arial', 12, 'bold'))
        self.live_scanner_btn.pack(side='left', padx=5)

        # Manual scan button
        manual_scan_btn = tk.Button(controls_frame,
                                   text='🔍 Manual Scan (5-Criteria)',
                                   command=self.run_manual_scan,
                                   bg='#2980b9', fg='white',
                                   font=('Arial', 12, 'bold'))
        manual_scan_btn.pack(side='left', padx=5)

        # Alternative scanner button
        alt_scan_btn = tk.Button(controls_frame,
                                text='📊 Alternative Scanner',
                                command=self.run_alternative_scan,
                                bg='#27ae60', fg='white',
                                font=('Arial', 12, 'bold'))
        alt_scan_btn.pack(side='left', padx=5)

        # Status frame
        status_frame = tk.Frame(scanner_frame, bg='white')
        status_frame.pack(fill='x', padx=10, pady=5)

        self.status_label = tk.Label(status_frame, text='Status: Ready',
                                    font=('Arial', 11), bg='white', fg='green')
        self.status_label.pack(side='left')

        # Results frame
        results_frame = tk.Frame(scanner_frame, bg='white')
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Results table
        columns = ('Symbol', 'Timeframe', 'Grade', 'Confidence', 'Price', 'Criteria', 'Time')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100)

        # Scrollbar for results
        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)

        self.results_tree.pack(side='left', fill='both', expand=True)
        results_scrollbar.pack(side='right', fill='y')

        # Alerts frame
        alerts_frame = tk.Frame(scanner_frame, bg='white')
        alerts_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(alerts_frame, text='📢 Real-time Alerts:',
                font=('Arial', 12, 'bold'), bg='white').pack(anchor='w')

        self.alerts_text = scrolledtext.ScrolledText(alerts_frame, height=8, width=100,
                                                    bg='#2c3e50', fg='white',
                                                    font=('Consolas', 9))
        self.alerts_text.pack(fill='x', pady=5)

        # Initial alert
        self.add_alert("🎯 TotalRecall Scanner Ready - Your 5-criteria pattern loaded!")
        self.add_alert("📊 Click 'Live Scanner' to start automated scanning every 5 minutes")

    def create_options_tab(self):
        """Create options analysis interface."""
        options_frame = ttk.Frame(self.notebook)
        self.notebook.add(options_frame, text='📊 Options Analysis')

        # Title
        title = tk.Label(options_frame, text='📊 Options Trading Analysis',
                        font=('Arial', 16, 'bold'), bg='white')
        title.pack(pady=10)

        # Placeholder content
        content = tk.Label(options_frame,
                          text='Options analysis features will be implemented here.\n\n' +
                               'This will include:\n' +
                               '• Options flow analysis\n' +
                               '• Strategy recommendations\n' +
                               '• Risk/reward calculations\n' +
                               '• Greeks analysis',
                          font=('Arial', 12), bg='white', justify='left')
        content.pack(pady=20)

    def create_positions_tab(self):
        """Create enhanced position management and trading interface."""
        positions_frame = ttk.Frame(self.notebook)
        self.notebook.add(positions_frame, text='📈 Position Manager')

        # Title
        title = tk.Label(positions_frame, text='📈 Position Management & Trading',
                        font=('Arial', 16, 'bold'), bg='white')
        title.pack(pady=10)

        # Portfolio Summary Frame
        summary_frame = tk.LabelFrame(positions_frame, text='💰 Portfolio Summary',
                                     font=('Arial', 12, 'bold'), bg='white')
        summary_frame.pack(fill='x', padx=10, pady=5)

        # Portfolio metrics
        self.portfolio_labels = {}
        metrics = [
            ('Total Value', 'total_value'),
            ('Available Cash', 'available_cash'),
            ('Unrealized P&L', 'unrealized_pnl'),
            ('Realized P&L', 'realized_pnl'),
            ('Active Positions', 'active_positions')
        ]

        for i, (label, key) in enumerate(metrics):
            row = i // 3
            col = i % 3

            label_widget = tk.Label(summary_frame, text=f'{label}:',
                                   font=('Arial', 10, 'bold'), bg='white')
            label_widget.grid(row=row*2, column=col, sticky='w', padx=10, pady=2)

            value_widget = tk.Label(summary_frame, text='$0.00',
                                   font=('Arial', 10), bg='white', fg='green')
            value_widget.grid(row=row*2+1, column=col, sticky='w', padx=10, pady=2)

            self.portfolio_labels[key] = value_widget

        # Manual Trading Frame
        trading_frame = tk.LabelFrame(positions_frame, text='🔄 Manual Trading',
                                     font=('Arial', 12, 'bold'), bg='white')
        trading_frame.pack(fill='x', padx=10, pady=5)

        # Trading controls
        controls_row1 = tk.Frame(trading_frame, bg='white')
        controls_row1.pack(fill='x', padx=5, pady=5)

        tk.Label(controls_row1, text='Symbol:', font=('Arial', 10), bg='white').pack(side='left')
        self.trade_symbol = tk.Entry(controls_row1, font=('Arial', 10), width=8)
        self.trade_symbol.pack(side='left', padx=5)

        tk.Label(controls_row1, text='Quantity:', font=('Arial', 10), bg='white').pack(side='left', padx=(10,0))
        self.trade_quantity = tk.Entry(controls_row1, font=('Arial', 10), width=8)
        self.trade_quantity.pack(side='left', padx=5)

        tk.Label(controls_row1, text='Action:', font=('Arial', 10), bg='white').pack(side='left', padx=(10,0))
        self.trade_action = ttk.Combobox(controls_row1, values=['Buy', 'Sell'], width=6)
        self.trade_action.set('Buy')
        self.trade_action.pack(side='left', padx=5)

        # Trading buttons
        controls_row2 = tk.Frame(trading_frame, bg='white')
        controls_row2.pack(fill='x', padx=5, pady=5)

        execute_btn = tk.Button(controls_row2, text='🚀 Execute Trade',
                               command=self.execute_manual_trade,
                               bg='#e74c3c', fg='white', font=('Arial', 10, 'bold'))
        execute_btn.pack(side='left', padx=5)

        refresh_btn = tk.Button(controls_row2, text='🔄 Refresh Portfolio',
                               command=self.refresh_portfolio,
                               bg='#3498db', fg='white', font=('Arial', 10, 'bold'))
        refresh_btn.pack(side='left', padx=5)

        # Positions Table
        positions_table_frame = tk.LabelFrame(positions_frame, text='📊 Active Positions',
                                             font=('Arial', 12, 'bold'), bg='white')
        positions_table_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Positions tree
        pos_columns = ('Symbol', 'Shares', 'Entry Price', 'Current Price', 'P&L $', 'P&L %', 'Value')
        self.positions_tree = ttk.Treeview(positions_table_frame, columns=pos_columns, show='headings', height=8)

        for col in pos_columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=100)

        # Scrollbar for positions
        pos_scrollbar = ttk.Scrollbar(positions_table_frame, orient='vertical', command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=pos_scrollbar.set)

        self.positions_tree.pack(side='left', fill='both', expand=True)
        pos_scrollbar.pack(side='right', fill='y')

        # Trading Status
        status_frame = tk.Frame(positions_frame, bg='white')
        status_frame.pack(fill='x', padx=10, pady=5)

        self.trading_status = tk.Label(status_frame, text='Status: Ready for trading',
                                      font=('Arial', 11), bg='white', fg='green')
        self.trading_status.pack(side='left')

        # Initial portfolio refresh
        self.refresh_portfolio()

    def execute_manual_trade(self):
        """Execute a manual trade."""
        try:
            symbol = self.trade_symbol.get().strip().upper()
            quantity_str = self.trade_quantity.get().strip()
            action = self.trade_action.get().lower()

            # Validation
            if not symbol:
                self.trading_status.config(text='Status: Please enter a symbol', fg='red')
                return

            if not quantity_str:
                self.trading_status.config(text='Status: Please enter quantity', fg='red')
                return

            try:
                quantity = int(quantity_str)
                if quantity <= 0:
                    raise ValueError("Quantity must be positive")
            except ValueError:
                self.trading_status.config(text='Status: Invalid quantity', fg='red')
                return

            # Confirmation dialog
            from tkinter import messagebox
            confirm_msg = f"Execute {action.upper()} order for {quantity} shares of {symbol}?"
            if not messagebox.askyesno("Confirm Trade", confirm_msg):
                return

            # Update status
            self.trading_status.config(text=f'Status: Executing {action} order for {symbol}...', fg='orange')

            # Execute trade in background thread
            def trade_thread():
                try:
                    result = self.trading_manager.execute_trade(symbol, action, quantity)

                    # Update GUI
                    if result.get('success'):
                        self.root.after(0, lambda: self.trading_status.config(
                            text=f'Status: {result["message"]}', fg='green'))
                        self.root.after(0, lambda: self.add_alert(f"✅ {result['message']}"))

                        # Clear inputs
                        self.root.after(0, lambda: self.trade_symbol.delete(0, tk.END))
                        self.root.after(0, lambda: self.trade_quantity.delete(0, tk.END))

                        # Refresh portfolio
                        self.root.after(0, self.refresh_portfolio)
                    else:
                        self.root.after(0, lambda: self.trading_status.config(
                            text=f'Status: {result["message"]}', fg='red'))
                        self.root.after(0, lambda: self.add_alert(f"❌ {result['message']}"))

                except Exception as e:
                    self.root.after(0, lambda: self.trading_status.config(
                        text=f'Status: Trade error: {e}', fg='red'))
                    self.root.after(0, lambda: self.add_alert(f"❌ Trade error: {e}"))

            # Start trade thread
            threading.Thread(target=trade_thread, daemon=True).start()

        except Exception as e:
            self.trading_status.config(text=f'Status: Error: {e}', fg='red')
            self.add_alert(f"❌ Trading error: {e}")

    def refresh_portfolio(self):
        """Refresh portfolio data and display."""
        try:
            # Get portfolio summary
            portfolio = self.trading_manager.get_portfolio_summary()

            if 'error' in portfolio:
                self.trading_status.config(text=f'Status: Portfolio error: {portfolio["error"]}', fg='red')
                return

            # Update portfolio labels
            self.portfolio_labels['total_value'].config(text=f"${portfolio.get('total_value', 0):,.2f}")
            self.portfolio_labels['available_cash'].config(text=f"${portfolio.get('available_cash', 0):,.2f}")

            unrealized_pnl = portfolio.get('unrealized_pnl', 0)
            self.portfolio_labels['unrealized_pnl'].config(
                text=f"${unrealized_pnl:,.2f}",
                fg='green' if unrealized_pnl >= 0 else 'red'
            )

            realized_pnl = portfolio.get('realized_pnl', 0)
            self.portfolio_labels['realized_pnl'].config(
                text=f"${realized_pnl:,.2f}",
                fg='green' if realized_pnl >= 0 else 'red'
            )

            self.portfolio_labels['active_positions'].config(text=str(portfolio.get('active_positions', 0)))

            # Update positions table
            for item in self.positions_tree.get_children():
                self.positions_tree.delete(item)

            positions = portfolio.get('positions', {})
            for symbol, pos_data in positions.items():
                shares = pos_data.get('shares', 0)
                entry_price = pos_data.get('entry_price', 0)
                current_price = pos_data.get('current_price', entry_price)
                unrealized_pnl = pos_data.get('unrealized_pnl', 0)

                # Calculate P&L percentage
                pnl_pct = ((current_price - entry_price) / entry_price * 100) if entry_price > 0 else 0
                position_value = shares * current_price

                # Insert row with color coding
                item = self.positions_tree.insert('', 'end', values=(
                    symbol,
                    shares,
                    f"${entry_price:.2f}",
                    f"${current_price:.2f}",
                    f"${unrealized_pnl:.2f}",
                    f"{pnl_pct:+.1f}%",
                    f"${position_value:.2f}"
                ))

                # Color code based on P&L
                if unrealized_pnl > 0:
                    self.positions_tree.set(item, 'P&L $', f"+${unrealized_pnl:.2f}")
                elif unrealized_pnl < 0:
                    self.positions_tree.set(item, 'P&L $', f"-${abs(unrealized_pnl):.2f}")

            self.trading_status.config(text='Status: Portfolio updated', fg='green')

        except Exception as e:
            self.trading_status.config(text=f'Status: Refresh error: {e}', fg='red')
            print(f"Portfolio refresh error: {e}")

    def send_chat_message(self, event=None):
        """Send chat message."""
        try:
            message = self.chat_input.get().strip()
            if not message:
                return

            # Clear input
            self.chat_input.delete(0, tk.END)

            # Add user message to chat
            self.chat_output.insert(tk.END, f"\n🧑 You: {message}\n")
            self.chat_output.see(tk.END)

            # Get response
            response = self.chat_manager.send_message(message)

            # Add response to chat
            self.chat_output.insert(tk.END, f"\n🤖 Assistant: {response}\n")
            self.chat_output.see(tk.END)

        except Exception as e:
            self.chat_output.insert(tk.END, f"\n❌ Error: {e}\n")
            self.chat_output.see(tk.END)

    def toggle_live_scanner(self):
        """Toggle live scanner on/off."""
        try:
            if self.live_scanner.is_running:
                # Stop scanner
                if self.live_scanner.stop():
                    self.live_scanner_btn.config(text='🎯 Live Scanner (5-Criteria Pattern)', bg='#e74c3c')
                    self.status_label.config(text='Status: Scanner Stopped', fg='red')
                    self.add_alert("⏹️ Live scanner stopped")
                else:
                    self.add_alert("❌ Failed to stop scanner")
            else:
                # Start scanner
                if self.live_scanner.start():
                    self.live_scanner_btn.config(text='⏹️ Stop Live Scanner', bg='#c0392b')
                    self.status_label.config(text='Status: Live Scanner Running (5-min intervals)', fg='green')
                    self.add_alert("🚀 Live scanner started!")
                    self.add_alert("🎯 Scanning S&P 500 + $100B+ stocks every 5 minutes")
                else:
                    self.add_alert("❌ Failed to start scanner")

        except Exception as e:
            self.add_alert(f"❌ Scanner error: {e}")

    def run_manual_scan(self):
        """Run manual scan."""
        try:
            self.add_alert("🔍 Starting manual scan...")
            self.status_label.config(text='Status: Manual Scanning...', fg='orange')

            def scan_thread():
                try:
                    # Get symbols
                    symbols = self.live_scanner.symbol_manager.get_trading_symbols(100)  # Smaller set for manual

                    # Run scan
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        results = loop.run_until_complete(self.live_scanner._scan_symbols_async(symbols))

                        # Update GUI
                        self.root.after(0, lambda: self.display_results(results, "Manual Scan"))
                        self.root.after(0, lambda: self.add_alert(f"✅ Manual scan complete: {len(results)} setups found"))
                        self.root.after(0, lambda: self.status_label.config(text='Status: Ready', fg='green'))

                    finally:
                        loop.close()

                except Exception as e:
                    self.root.after(0, lambda: self.add_alert(f"❌ Manual scan error: {e}"))
                    self.root.after(0, lambda: self.status_label.config(text='Status: Error', fg='red'))

            # Start scan in background
            threading.Thread(target=scan_thread, daemon=True).start()

        except Exception as e:
            self.add_alert(f"❌ Error starting manual scan: {e}")

    def run_alternative_scan(self):
        """Run alternative scanner."""
        try:
            self.add_alert("📊 Running alternative scanner...")
            self.status_label.config(text='Status: Alternative Scanning...', fg='orange')

            # Simple alternative scan (just show some sample results)
            sample_results = [
                {
                    'symbol': 'AAPL',
                    'timeframe': '15min',
                    'grade': 'B',
                    'confidence': 0.72,
                    'criteria_count': 3,
                    'price': 189.50,
                    'timestamp': datetime.now(),
                    'scanner_type': 'alternative'
                },
                {
                    'symbol': 'MSFT',
                    'timeframe': '15min',
                    'grade': 'B',
                    'confidence': 0.68,
                    'criteria_count': 3,
                    'price': 378.25,
                    'timestamp': datetime.now(),
                    'scanner_type': 'alternative'
                }
            ]

            self.display_results(sample_results, "Alternative Scanner")
            self.add_alert("✅ Alternative scan complete (sample results)")
            self.status_label.config(text='Status: Ready', fg='green')

        except Exception as e:
            self.add_alert(f"❌ Alternative scan error: {e}")

    def display_results(self, results: List[Dict], scan_type: str):
        """Display scan results in the table."""
        try:
            # Clear existing results
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # Add new results
            for result in results:
                self.results_tree.insert('', 'end', values=(
                    result['symbol'],
                    result['timeframe'],
                    result['grade'],
                    f"{result['confidence']:.1%}",
                    f"${result['price']:.2f}",
                    f"{result['criteria_count']}/5",
                    result['timestamp'].strftime('%H:%M:%S')
                ))

            self.add_alert(f"📊 {scan_type}: Displayed {len(results)} setups in table")

        except Exception as e:
            self.add_alert(f"❌ Error displaying results: {e}")

    def add_alert(self, message: str):
        """Add alert to alerts display."""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            alert_msg = f"[{timestamp}] {message}\n"

            self.alerts_text.insert(tk.END, alert_msg)
            self.alerts_text.see(tk.END)

            # Keep only last 100 lines
            lines = self.alerts_text.get('1.0', tk.END).split('\n')
            if len(lines) > 100:
                self.alerts_text.delete('1.0', f'{len(lines)-100}.0')

        except Exception as e:
            print(f"Error adding alert: {e}")

    def start_alert_monitoring(self):
        """Start monitoring for live scanner alerts."""
        def monitor_alerts():
            try:
                # Check for new alerts from live scanner
                alert = self.live_scanner.get_alert()
                if alert:
                    self.add_alert(alert)

                # Check for new results
                results = self.live_scanner.get_results()
                if results:
                    self.display_results(results, "Live Scanner")

                # Update scanner stats
                stats = self.live_scanner.get_stats()
                if stats.get('is_running'):
                    total_scans = stats.get('total_scans', 0)
                    if total_scans > 0:
                        self.status_label.config(
                            text=f'Status: Live Scanner Active ({total_scans} scans completed)',
                            fg='green'
                        )

            except Exception as e:
                print(f"Alert monitoring error: {e}")

            # Schedule next check
            self.root.after(1000, monitor_alerts)  # Check every second

        # Start monitoring
        monitor_alerts()

    def run(self):
        """Run the application."""
        try:
            print("🚀 Starting TotalRecall Trading System - Clean")
            print("🎯 Your 5-criteria pandas_ta pattern loaded as primary scanner")
            print("📊 Interface ready with simplified, professional layout")

            self.root.mainloop()

        except Exception as e:
            print(f"❌ Application error: {e}")
        finally:
            # Clean shutdown
            if self.live_scanner.is_running:
                self.live_scanner.stop()


def main():
    """Main entry point."""
    try:
        # Check dependencies
        if not DEPENDENCIES_AVAILABLE:
            print("⚠️ Some dependencies missing. Install with:")
            print("pip install -r requirements.txt")
            print("\nRunning with limited functionality...")

        if not PANDAS_TA_AVAILABLE:
            print("⚠️ pandas_ta not available. Using manual implementation.")
            print("For full functionality, install: pip install pandas-ta")

        # Create and run application
        app = TotalRecallApp()
        app.run()

    except KeyboardInterrupt:
        print("\n👋 TotalRecall shutdown requested")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
