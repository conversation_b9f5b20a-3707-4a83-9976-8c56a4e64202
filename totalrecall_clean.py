#!/usr/bin/env python3
"""
TotalRecall Trading System - Clean Rebuild
Simplified architecture with your 5-criteria pandas_ta pattern as the primary scanner
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import queue
import sys
import os

# Add paths for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'scanners'))

# Core imports
try:
    from core.logger_util import info, warning, error
except ImportError:
    def info(msg): print(f"ℹ️ {msg}")
    def warning(msg): print(f"⚠️ {msg}")
    def error(msg): print(f"❌ {msg}")

try:
    from scanners.pandas_ta_ttm_scanner import scan_comprehensive_pandas_ta, scan_with_pandas_ta
    from core.symbol_manager import get_trading_symbols
    from core.ttm_setup_manager import store_ttm_setup, get_recent_setups
    from core.chat_gpt import chat_gpt
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    warning(f"Some dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False
    
    # Fallback functions
    async def scan_comprehensive_pandas_ta(*args, **kwargs):
        return []
    
    async def scan_with_pandas_ta(*args, **kwargs):
        return []
    
    def get_trading_symbols():
        return ['AAPL', 'MSFT', 'NVDA', 'GOOGL', 'TSLA']
    
    def store_ttm_setup(data):
        pass
    
    def get_recent_setups(limit=10):
        return []
    
    def chat_gpt(message):
        return "Chat functionality not available - install dependencies"


class LiveScanner:
    """Simplified live scanner for your 5-criteria pandas_ta pattern."""
    
    def __init__(self):
        self.is_running = False
        self.scanner_thread = None
        self.scan_interval = 300  # 5 minutes
        self.alert_queue = queue.Queue()
        self.results_queue = queue.Queue()
        
        self.stats = {
            "total_scans": 0,
            "setups_found": 0,
            "last_scan_time": None,
            "is_running": False
        }
    
    def start(self) -> bool:
        """Start live scanning."""
        if self.is_running:
            return False
        
        if not DEPENDENCIES_AVAILABLE:
            self.alert_queue.put("❌ Dependencies not available")
            return False
        
        self.is_running = True
        self.stats["is_running"] = True
        self.scanner_thread = threading.Thread(target=self._scan_loop, daemon=True)
        self.scanner_thread.start()
        
        self.alert_queue.put("🚀 Live Scanner Started!")
        self.alert_queue.put("🎯 5-Criteria Pattern scanning every 5 minutes")
        return True
    
    def stop(self) -> bool:
        """Stop live scanning."""
        if not self.is_running:
            return False
        
        self.is_running = False
        self.stats["is_running"] = False
        self.alert_queue.put("⏹️ Live Scanner Stopped")
        return True
    
    def _scan_loop(self):
        """Main scanning loop."""
        while self.is_running:
            try:
                # Check market hours (simplified)
                current_hour = datetime.now().hour
                if 4 <= current_hour <= 20:  # 4 AM to 8 PM ET (simplified)
                    self._perform_scan()
                else:
                    self.alert_queue.put("⏰ Market closed - scanner waiting")
                
                # Wait for next scan
                time.sleep(self.scan_interval)
                
            except Exception as e:
                error(f"Scan loop error: {e}")
                time.sleep(60)
    
    def _perform_scan(self):
        """Perform a single scan."""
        try:
            self.alert_queue.put("🔍 Starting 5-criteria pattern scan...")
            
            # Run async scan
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                results = loop.run_until_complete(scan_comprehensive_pandas_ta(
                    timeframe="15min",
                    max_concurrent=10
                ))
                
                # Process results
                high_quality = [r for r in results if r.get('grade') in ['A+', 'A', 'B']]
                
                # Update stats
                self.stats["total_scans"] += 1
                self.stats["setups_found"] = len(results)
                self.stats["last_scan_time"] = datetime.now()
                
                # Store results
                for result in results:
                    try:
                        store_ttm_setup({
                            'symbol': result['symbol'],
                            'grade': result['grade'],
                            'confidence': result['confidence'],
                            'price': result['price'],
                            'scanner_type': '5_criteria_primary'
                        })
                    except:
                        pass
                
                # Send alerts
                self.alert_queue.put(f"✅ Scan complete: {len(results)} setups ({len(high_quality)} high-quality)")
                
                if high_quality:
                    self.alert_queue.put(f"🎯 HIGH-QUALITY SETUPS FOUND: {len(high_quality)}")
                    for setup in high_quality[:3]:
                        self.alert_queue.put(f"⭐ {setup['symbol']} Grade {setup['grade']}: ${setup['price']:.2f}")
                
                # Queue results for display
                self.results_queue.put(results)
                
            finally:
                loop.close()
                
        except Exception as e:
            error(f"Scan error: {e}")
            self.alert_queue.put(f"❌ Scan error: {e}")
    
    def get_alert(self) -> Optional[str]:
        """Get next alert."""
        try:
            return self.alert_queue.get_nowait()
        except queue.Empty:
            return None
    
    def get_results(self) -> Optional[List[Dict]]:
        """Get latest results."""
        try:
            return self.results_queue.get_nowait()
        except queue.Empty:
            return None


class TotalRecallClean:
    """Clean, simplified TotalRecall trading interface."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("TotalRecall Trading System - Clean")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')
        
        # Core components
        self.live_scanner = LiveScanner()
        self.monitoring_active = False
        
        # Create interface
        self.create_interface()
        
        # Start monitoring
        self.start_monitoring()
    
    def create_interface(self):
        """Create the main interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_chat_tab()
        self.create_scanner_tab()
        self.create_options_tab()
        self.create_positions_tab()
    
    def create_chat_tab(self):
        """Create AI chat interface."""
        chat_frame = ttk.Frame(self.notebook)
        self.notebook.add(chat_frame, text='💬 AI Chat')
        
        # Title
        title = tk.Label(chat_frame, text='💬 AI Trading Assistant', 
                        font=('Arial', 16, 'bold'), bg='white')
        title.pack(pady=10)
        
        # Chat output
        self.chat_output = scrolledtext.ScrolledText(chat_frame, height=25, width=100,
                                                    bg='#1e1e1e', fg='white', 
                                                    font=('Consolas', 10))
        self.chat_output.pack(padx=10, pady=5, fill='both', expand=True)
        
        # Input frame
        input_frame = tk.Frame(chat_frame, bg='white')
        input_frame.pack(fill='x', padx=10, pady=5)
        
        self.chat_input = tk.Entry(input_frame, font=('Arial', 12))
        self.chat_input.pack(side='left', fill='x', expand=True, padx=(0, 5))
        self.chat_input.bind('<Return>', self.send_chat_message)
        
        send_btn = tk.Button(input_frame, text='Send', command=self.send_chat_message,
                            bg='#4CAF50', fg='white', font=('Arial', 12, 'bold'))
        send_btn.pack(side='right')
        
        # Welcome message
        self.chat_output.insert(tk.END, "🎯 TotalRecall AI Assistant Ready\n")
        self.chat_output.insert(tk.END, "Ask me about trading, TTM setups, or market analysis!\n\n")
