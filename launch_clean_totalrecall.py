#!/usr/bin/env python3
"""
Launch script for the new clean TotalRecall system
"""

import os
import sys
import subprocess

def main():
    """Launch the clean TotalRecall system."""
    try:
        print("🚀 Launching TotalRecall Trading System - Clean Rebuild")
        print("=" * 60)
        print("🎯 Features:")
        print("   • Your 5-criteria pandas_ta pattern as primary scanner")
        print("   • Live scanning every 5 minutes during market hours")
        print("   • AI chat assistance for trading questions")
        print("   • Clean, simplified interface with clear button purposes")
        print("   • Database integration for setup storage")
        print("   • Real-time alerts for high-quality setups")
        print("=" * 60)
        
        # Check if the new system file exists
        script_path = os.path.join(os.path.dirname(__file__), 'totalrecall_new.py')
        
        if not os.path.exists(script_path):
            print(f"❌ Error: {script_path} not found")
            return 1
        
        print(f"📂 Launching: {script_path}")
        print("🔄 Starting application...")
        
        # Launch the new system
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=False)
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n👋 Launch cancelled by user")
        return 0
    except Exception as e:
        print(f"❌ Launch error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
