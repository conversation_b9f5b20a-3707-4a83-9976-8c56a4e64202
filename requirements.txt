# TotalRecall Enhanced - Required Dependencies
# Core trading and data analysis
pandas>=1.5.0
numpy>=1.21.0
requests>=2.28.0
python-dotenv>=0.19.0

# Financial data and APIs
yfinance>=0.1.87
alpaca-trade-api>=2.3.0

# Technical analysis
ta-lib>=0.4.25
TA-Lib>=0.4.25
pandas-ta>=0.3.14b

# Data visualization (optional but recommended)
matplotlib>=3.5.0
plotly>=5.10.0

# Web scraping and sentiment analysis
beautifulsoup4>=4.11.0
textblob>=0.17.0
praw>=7.6.0

# Database and storage
sqlite3  # Built into Python

# Async support
asyncio  # Built into Python
aiohttp>=3.8.0

# JSON and configuration
json5>=0.9.0

# Date and time handling
python-dateutil>=2.8.0

# HTTP client improvements
httpx>=0.23.0

# Optional: Advanced features
scikit-learn>=1.1.0  # For machine learning features
scipy>=1.9.0  # For statistical analysis

# MCP Integration
mcp>=0.1.0  # Model Context Protocol

# Development and testing (optional)
pytest>=7.0.0
black>=22.0.0
flake8>=5.0.0
